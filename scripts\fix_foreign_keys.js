const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
  user: process.env.DB_USER,
  host: process.env.DB_HOST,
  database: process.env.DB_NAME,
  password: process.env.DB_PASSWORD,
  port: process.env.DB_PORT,
});

async function fixForeignKeys() {
  try {
    console.log('Starting foreign key constraint fix...');
    
    // Check if site_deployments table exists
    const tableCheck = await pool.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'site_deployments'
      );
    `);
    
    if (!tableCheck.rows[0].exists) {
      console.log('site_deployments table does not exist. No changes needed.');
      return;
    }
    
    console.log('site_deployments table found. Updating foreign key constraint...');
    
    // Drop the existing constraint
    try {
      await pool.query('ALTER TABLE site_deployments DROP CONSTRAINT site_deployments_deployed_by_fkey');
      console.log('Dropped existing foreign key constraint');
    } catch (error) {
      console.log('Constraint may not exist, continuing...');
    }
    
    // Add the constraint back with SET NULL on delete
    await pool.query(`
      ALTER TABLE site_deployments 
      ADD CONSTRAINT site_deployments_deployed_by_fkey 
      FOREIGN KEY (deployed_by) REFERENCES ndt_users(id) ON DELETE SET NULL
    `);
    console.log('Added new foreign key constraint with ON DELETE SET NULL');
    
    // Verify the constraint
    const verifyResult = await pool.query(`
      SELECT 
          tc.constraint_name,
          tc.table_name,
          kcu.column_name,
          ccu.table_name AS foreign_table_name,
          ccu.column_name AS foreign_column_name,
          rc.delete_rule
      FROM information_schema.table_constraints AS tc 
      JOIN information_schema.key_column_usage AS kcu
          ON tc.constraint_name = kcu.constraint_name
      JOIN information_schema.constraint_column_usage AS ccu
          ON ccu.constraint_name = tc.constraint_name
      JOIN information_schema.referential_constraints AS rc
          ON tc.constraint_name = rc.constraint_name
      WHERE tc.constraint_type = 'FOREIGN KEY' 
          AND tc.constraint_name = 'site_deployments_deployed_by_fkey'
    `);
    
    if (verifyResult.rows.length > 0) {
      const constraint = verifyResult.rows[0];
      console.log('\nConstraint verification:');
      console.log(`- Constraint: ${constraint.constraint_name}`);
      console.log(`- Table: ${constraint.table_name}`);
      console.log(`- Column: ${constraint.column_name}`);
      console.log(`- References: ${constraint.foreign_table_name}.${constraint.foreign_column_name}`);
      console.log(`- Delete rule: ${constraint.delete_rule}`);
    }
    
    console.log('\nForeign key constraint fix completed successfully!');
    
  } catch (error) {
    console.error('Fix failed:', error);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

// Run the fix
fixForeignKeys();
