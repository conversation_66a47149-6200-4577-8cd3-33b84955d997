
/* SweetAlert2 Custom Styles */
.swal-popup {
  background: linear-gradient(135deg, #1e1e2e, #2a2a3e) !important;
  border: 2px solid rgba(255, 255, 255, 0.15) !important;
  border-radius: 16px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
}
.swal2-title {
  color: #f1f5f9 !important;
  font-weight: 700 !important;
  font-size: 20px !important;
}
.swal2-content {
  color: #cbd5e1 !important;
  font-size: 16px !important;
}
.swal-confirm-btn {
  background: linear-gradient(135deg, #4f46e5, #7c3aed) !important;
  border: none !important;
  border-radius: 8px !important;
  padding: 12px 24px !important;
  font-weight: 600 !important;
  box-shadow: 0 4px 16px rgba(79, 70, 229, 0.3) !important;
  transition: all 0.3s ease !important;
}
.swal-confirm-btn:hover {
  background: linear-gradient(135deg, #6366f1, #8b5cf6) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 20px rgba(79, 70, 229, 0.4) !important;
}
.swal-cancel-btn {
  background: linear-gradient(135deg, #e53e3e, #c53030) !important;
  border: none !important;
  border-radius: 8px !important;
  padding: 12px 24px !important;
  font-weight: 600 !important;
  box-shadow: 0 4px 16px rgba(229, 62, 62, 0.3) !important;
  transition: all 0.3s ease !important;
}
.swal-cancel-btn:hover {
  background: linear-gradient(135deg, #dc2626, #b91c1c) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 20px rgba(229, 62, 62, 0.4) !important;
}

/* Toast Notifications Custom Styles */
.Vue-Toastification__toast {
  border-radius: 12px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2) !important;
  border: 2px solid rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(10px) !important;
}
.toast-success {
  background: linear-gradient(135deg, #059669, #047857) !important;
  color: #ffffff !important;
  border-color: rgba(5, 150, 105, 0.3) !important;
}
.toast-error {
  background: linear-gradient(135deg, #dc2626, #b91c1c) !important;
  color: #ffffff !important;
  border-color: rgba(220, 38, 38, 0.3) !important;
}
.toast-body {
  font-weight: 600 !important;
  font-size: 14px !important;
  direction: rtl !important;
  text-align: right !important;
}
.Vue-Toastification__progress-bar {
  background: rgba(255, 255, 255, 0.3) !important;
}
.Vue-Toastification__close-button {
  color: rgba(255, 255, 255, 0.8) !important;
  font-size: 18px !important;
  font-weight: bold !important;
}
.Vue-Toastification__close-button:hover {
  color: #ffffff !important;
}


body {
    background-color: #121212;
    color: #e0e0e0;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0;
    padding: 0;
}
.navbar {
    background: linear-gradient(135deg, #1a1a2e, #16213e);
    border: 2px solid #3a3a5e;
    border-radius: 12px;
    margin: 16px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);
}
.navbar-content {
    padding: 16px 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 16px;
}
.navbar-text {
    display: flex;
    align-items: center;
    gap: 20px;
    flex-wrap: wrap;
    justify-content: center;
    flex: 1;
}
.org-name, .team-name {
    font-weight: 700;
    font-size: clamp(16px, 3vw, 24px);
    color: #f5f5f5;
    text-align: center;
    line-height: 1.4;
}
.logo {
    height: clamp(50px, 8vw, 70px);
    width: auto;
    border: 2px solid #4a5568;
    border-radius: 50%;
    transition: transform 0.3s ease;
}
.logo:hover {
    transform: scale(1.05);
}
.user-section {
    position: relative;
    z-index: 100;
}
.user-button {
    display: flex;
    align-items: center;
    gap: 12px;
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    padding: 8px 16px;
    cursor: pointer;
    transition: all 0.3s ease;
}
.user-button:hover {
    background: rgba(255, 255, 255, 0.12);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}
.user-avatar {
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}
.user-info {
    display: flex;
    align-items: center;
    gap: 8px;
}
.user-name {
    font-weight: 600;
    font-size: 14px;
    color: #f5f5f5;
    white-space: nowrap;
}
.dropdown-arrow {
    transition: transform 0.3s ease;
    color: #a0aec0;
}
.user-button:hover .dropdown-arrow {
    transform: rotate(180deg);
}
.user-menu {
    position: absolute;
    top: 100%;
    right: auto;
    left: 0;
    margin-top: 8px;
    background: #1a202c;
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    min-width: 180px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);
    overflow: hidden;
    animation: slideDown 0.3s ease;
}

/* Navigation Buttons */
.nav-buttons {
    display: flex;
    gap: 16px;
    align-items: center;
}

/* Arabic RTL alignment for navbar actions */
.nav-actions {
    display: flex;
    gap: 12px;
    flex-direction: row-reverse;
}
.nav-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    min-width: 160px; /* unified width with global buttons */
    justify-content: center;
    background: rgba(255, 255, 255, 0.08);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: #ffffff;
    font-weight: 700;
    font-size: 14px;
    letter-spacing: 0.2px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
}

/* Compact button utility for small actions */
.btn-compact {
    min-width: 96px;
    height: 40px;
    padding: 8px 14px;
    font-size: 13px;
    font-weight: 600;
}
.nav-btn:hover {
    background: rgba(255, 255, 255, 0.12);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}
.nav-btn.active {
    background: linear-gradient(135deg, #4f46e5, #7c3aed);
    border-color: #4f46e5;
    color: #ffffff;
    box-shadow: 0 2px 12px rgba(79, 70, 229, 0.3);
}
.nav-btn.active:hover {
    background: linear-gradient(135deg, #6366f1, #8b5cf6);
    transform: translateY(-1px);
    box-shadow: 0 4px 16px rgba(79, 70, 229, 0.4);
}

/* Main Content */
.main-content {
    margin: 24px;
    margin-left: auto;
    margin-right: auto;
}
.submit-view {
    display: flex;
    flex-direction: column;
    gap: 24px;
}
.submit-header {
    text-align: center;
    padding: 20px;
    background: rgba(255, 255, 255, 0.08);
    border: 2px solid rgba(255, 255, 255, 0.15);
    border-radius: 12px;
}
.submit-header h2 {
    color: #ffffff;
    font-size: 24px;
    font-weight: 700;
    margin: 0;
}

/* View Activities */
.view-activities {
    margin-top: 32px;
    padding: 0 8px;
}
.view-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 32px 24px;
    background: linear-gradient(135deg, rgba(79, 70, 229, 0.15), rgba(124, 58, 237, 0.15));
    border: 2px solid rgba(79, 70, 229, 0.3);
    border-radius: 16px;
    margin-bottom: 32px;
    position: relative;
    overflow: hidden;
}
.view-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #4f46e5, #7c3aed, #4f46e5);
}
.view-header-content {
    text-align: center;
    flex: 1;
}
.view-title {
    color: #ffffff;
    font-size: 28px;
    font-weight: 700;
    margin: 0 0 12px 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}
.view-description {
    color: #e2e8f0;
    font-size: 16px;
    margin: 0;
    opacity: 0.9;
}
.header-actions {
    display: flex;
    align-items: center;
    gap: 12px;
}
.export-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);
    min-width: 120px;
    height: auto;
}
.export-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, #d97706, #b45309);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.4);
}
.export-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
    background: linear-gradient(135deg, #6b7280, #4b5563);
}
.refresh-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
    min-width: 100px;
    height: auto;
}
.refresh-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, #059669, #047857);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
}
.refresh-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}
.refresh-btn svg.spinning {
    animation: spin 1s linear infinite;
}
@keyframes spin {
from { transform: rotate(0deg);
}
to { transform: rotate(360deg);
}
}
.my-activities-container {
    margin-top: 24px;
}
.activities-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
    gap: 24px;
    margin-top: 24px;
    padding: 0 4px;
}
.activity-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.08), rgba(255, 255, 255, 0.04));
    border: 2px solid rgba(255, 255, 255, 0.15);
    border-radius: 16px;
    padding: 24px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}
.activity-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #4f46e5, #7c3aed);
    transform: scaleX(0);
    transition: transform 0.4s ease;
}
.activity-card:hover {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.12), rgba(255, 255, 255, 0.08));
    transform: translateY(-4px);
    box-shadow: 0 8px 32px rgba(79, 70, 229, 0.2);
    border-color: rgba(79, 70, 229, 0.4);
}
.activity-card:hover::before {
    transform: scaleX(1);
}
.activity-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 2px solid rgba(79, 70, 229, 0.2);
}
.activity-header h4 {
    color: #ffffff;
    font-size: 20px;
    font-weight: 700;
    margin: 0;
    line-height: 1.3;
    flex: 1;
    margin-right: 16px;
}
.activity-actions {
    display: flex;
    gap: 10px;
    flex-shrink: 0;
}
.edit-btn, .delete-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    height: 40px;
    min-width: 120px;
    padding: 10px 16px;
    border-radius: 10px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: none;
    text-align: center;
}
.edit-btn {
    background: linear-gradient(135deg, #3182ce, #2c5aa0);
    color: white;
    box-shadow: 0 2px 8px rgba(49, 130, 206, 0.3);
}
.edit-btn:hover {
    background: linear-gradient(135deg, #2c5aa0, #2a4d8a);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(49, 130, 206, 0.4);
}
.delete-btn {
    background: linear-gradient(135deg, #e53e3e, #c53030);
    color: white;
    box-shadow: 0 2px 8px rgba(229, 62, 62, 0.3);
}
.delete-btn:hover {
    background: linear-gradient(135deg, #c53030, #a02626);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(229, 62, 62, 0.4);
}
.activity-details {
    margin-top: 16px;
}
.activity-details p {
    color: #e2e8f0;
    font-size: 15px;
    margin: 12px 0;
    line-height: 1.6;
    display: flex;
    align-items: center;
    gap: 8px;
}
.activity-details strong {
    color: #ffffff;
    font-weight: 700;
    min-width: 100px;
    display: inline-block;
}
.status-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    background: linear-gradient(135deg, #4f46e5, #7c3aed);
    color: #ffffff;
    text-align: center;
    box-shadow: 0 2px 8px rgba(79, 70, 229, 0.3);
    border: 1px solid rgba(79, 70, 229, 0.4);
}
.no-activities-message, .loading-message {
    text-align: center;
    padding: 48px 24px;
    color: #cbd5e0;
    font-size: 18px;
    font-weight: 500;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.02));
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    margin: 24px 0;
}
.loading-message {
    background: linear-gradient(135deg, rgba(79, 70, 229, 0.1), rgba(124, 58, 237, 0.1));
    border-color: rgba(79, 70, 229, 0.2);
    animation: pulse 2s infinite;
}
@keyframes pulse {
0%, 100% { opacity: 1;
}
50% { opacity: 0.7;
}
}
@keyframes slideDown {
from {
        opacity: 0;
        transform: translateY(-10px);
}
to {
        opacity: 1;
        transform: translateY(0);
}
}
.user-menu-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    color: #f5f5f5;
    font-size: 14px;
}
.user-menu-item:hover {
    background: rgba(255, 255, 255, 0.1);
}
.user-menu-item svg {
    color: #e53e3e;
}
span {
    font-weight: 600;
    color: #f5f5f5;
}
.view {
    padding: 16px;
    background: linear-gradient(135deg, #1e1e2e, #2a2a3e);
    border: 1px solid #3a3a4e;
    border-radius: 20px;
    max-width: 90%;
    margin: 16px auto;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}
button {
    background: linear-gradient(135deg, #4f46e5, #7c3aed);
    color: #ffffff;
    height: 48px;
    min-width: 160px;
    border: none;
    border-radius: 12px;
    padding: 12px 24px;
    cursor: pointer;
    font-weight: 700;
    font-size: 15px;
    letter-spacing: 0.2px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 16px rgba(79, 70, 229, 0.3);
}

/* Make icon+label buttons look consistent */
button svg { flex-shrink: 0;
}
button span { display: inline-block;
}

/* Inputs: improve placeholder visibility */
input::-moz-placeholder, select::-moz-placeholder, textarea::-moz-placeholder {
    color: #cbd5e1; /* brighter placeholder */
    opacity: 1;
}
input::placeholder,
select::placeholder,
textarea::placeholder {
    color: #cbd5e1; /* brighter placeholder */
    opacity: 1;
}

/* Edge/Firefox vendor prefixes */
input::-ms-input-placeholder { color: #cbd5e1;
}
input::-webkit-input-placeholder { color: #cbd5e1;
}
textarea::-webkit-input-placeholder { color: #cbd5e1;
}
select::-ms-input-placeholder { color: #cbd5e1;
}
button:hover {
    background: linear-gradient(135deg, #6366f1, #8b5cf6);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(79, 70, 229, 0.4);
}
button:active {
    transform: translateY(0);
}
button span {
    font-size: 16px;
    font-weight: 600;
}
.info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 24px;
    margin-top: 24px;
}
.base-info-container,
.activities-info-container {
    display: flex;
    flex-direction: column;
    gap: 16px;
    direction: rtl;
    background: rgba(255, 255, 255, 0.08);
    border: 2px solid rgba(255, 255, 255, 0.15);
    border-radius: 12px;
    padding: 24px;
}
.base-info-label {
    font-size: 18px;
    font-weight: 700;
    color: #f1f5f9;
    margin-bottom: 16px;
    text-align: right;
    border-bottom: 2px solid #4f46e5;
    padding-bottom: 8px;
}
.field-label {
    font-size: 14px;
    font-weight: 600;
    color: #cbd5e1;
    margin-bottom: 8px;
    text-align: right;
    display: block;
}
input[type="text"],
input[type="date"],
input[type="time"],
input[type='stat-number'],
textarea, select {
    direction: rtl;
    width: 100%;
    max-width: 400px;
    margin: 8px 0;
    padding: 12px 16px;
    background: rgba(255, 255, 255, 0.12);
    color: #ffffff;
    border: 2px solid rgba(255, 255, 255, 0.25);
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
}
input[type='stat-number'] {
    width: 300px;
}
input[type="text"]:focus,
input[type="date"]:focus,
select:focus {
    outline: none;
    border-color: #4f46e5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.2);
    background: rgba(255, 255, 255, 0.18);
    color: #ffffff;
}
select {
    cursor: pointer;
}
select option {
    background: #1a1a2e;
    color: #f0f0f0;
    padding: 8px;
}
.activity-item {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 16px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.08);
    border: 2px solid rgba(255, 255, 255, 0.15);
    border-radius: 8px;
    transition: all 0.3s ease;
}
.activity-item:hover {
    background: rgba(255, 255, 255, 0.12);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}
.activity-file-input {
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    width: -moz-fit-content;
    width: fit-content;
    max-width: 200px;
    text-align: center;
    padding: 12px 16px;
    font-size: 14px;
    background: rgba(255, 255, 255, 0.1);
    color: #f0f0f0;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    align-self: flex-start;
}
.activity-file-input:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: #4f46e5;
}
.activity-delete-button {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
    border: none;
    border-radius: 12px;
    margin: 8px 0;
    padding: 10px 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    box-shadow: 0 4px 16px rgba(239, 68, 68, 0.3);
}
.activity-delete-button:hover {
    background: linear-gradient(135deg, #f87171, #ef4444);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(239, 68, 68, 0.4);
}

/* Responsive Design */
@media (max-width: 768px) {
.navbar-content {
        flex-direction: column;
        text-align: center;
        gap: 12px;
}
.navbar-text {
        flex-direction: column;
        gap: 12px;
}
.org-name, .team-name {
        font-size: 18px;
}
.user-button {
        width: 100%;
        justify-content: center;
}
.info {
        grid-template-columns: 1fr;
        gap: 16px;
}
.base-info-container,
    .activities-info-container {
        padding: 16px;
}
.view {
        margin: 8px;
        padding: 12px;
}
}
@media (max-width: 480px) {
.navbar {
        margin: 8px;
}
.navbar-content {
        padding: 12px 16px;
}
.org-name, .team-name {
        font-size: 16px;
}
.logo {
        height: 50px;
}
.user-name {
        font-size: 12px;
}
input[type="text"],
    input[type="date"],
    select {
        font-size: 16px; /* Prevents zoom on iOS */
}
}

/* RTL Improvements */
.view {
    direction: rtl;
}
.navbar-content {
    flex-direction: row-reverse;
}
.navbar-text {
    flex-direction: row-reverse;
    text-align: right;
}
.nav-actions .nav-btn {
    flex-direction: row-reverse;
}
.view-header-content,
.view-title,
.view-description {
    text-align: right;
}
.activities-list {
    direction: rtl;
}
label {
    text-align: right;
    font-weight: 600;
    color: #cbd5e1;
    margin-bottom: 4px;
    display: block;
}

/* Inputs RTL */
input[type="text"],
input[type="date"],
select,
textarea {
    direction: rtl;
    text-align: right;
}

/* Fine color and spacing tweaks for Arabic */
.navbar {
    border-color: #4b5563;
}
.nav-btn {
    letter-spacing: 0.2px; /* tighter Arabic rhythm */
}
.view-header {
    border-color: rgba(79, 70, 229, 0.35);
}
.activity-card {
    padding-inline: 24px;
}
.activity-header h4 {
    margin-left: 0;
    margin-right: 15px; /* move spacing to the right for RTL */
}
.activity-details,
.activity-details p {
    text-align: right;
}
.splitter {
    height: 2px;
    background: linear-gradient(90deg, transparent, #4f46e5, transparent);
    margin: 24px 0;
    border-radius: 1px;
}

/* My Activities Section Styles */
.my-activities-section {
    margin: 30px 0;
    direction: rtl;
    text-align: right;
}
.toggle-activities-btn {
    width: 100%;
    padding: 15px 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}
.toggle-activities-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}
.toggle-icon {
    font-size: 14px;
    transition: transform 0.3s ease;
}
.my-activities-container {
    margin-top: 20px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}
.loading-message, .no-activities-message {
    text-align: center;
    padding: 40px 20px;
    color: #8892b0;
    font-size: 16px;
    font-weight: 500;
}

/* Activities Container */
.activities-container {
    margin-top: 20px;
    padding: 0;
}

/* Loading and Empty States */
.loading-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    color: #8892b0;
    font-size: 16px;
    font-weight: 500;
    gap: 16px;
}
.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(255, 255, 255, 0.1);
    border-top: 3px solid #4f46e5;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}
@keyframes spin {
0% { transform: rotate(0deg);
}
100% { transform: rotate(360deg);
}
}
.no-activities-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 80px 20px;
    text-align: center;
    color: #8892b0;
    gap: 16px;
}
.empty-icon {
    opacity: 0.6;
    margin-bottom: 8px;
}
.no-activities-message h3 {
    margin: 0;
    color: #e6f1ff;
    font-size: 24px;
    font-weight: 600;
}
.no-activities-message p {
    margin: 0;
    font-size: 16px;
    opacity: 0.8;
}

/* Edit Modal - Clean Flexbox Design */
.edit-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.75);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    padding: 20px;
    box-sizing: border-box;
}
.edit-modal {
    background: #1e293b;
    border: 1px solid #334155;
    border-radius: 12px;
    width: 100%;
    max-width: 600px;
    max-height: calc(100vh - 40px);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
}
.edit-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid #334155;
    background: #0f172a;
    flex-shrink: 0;
}
.edit-modal-header h3 {
    margin: 0;
    color: #f1f5f9;
    font-size: 18px;
    font-weight: 600;
}
.close-btn {
    background: none;
    border: none;
    color: #64748b;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}
.close-btn:hover {
    background: #334155;
    color: #f1f5f9;
}
.edit-form {
    padding: 24px;
    direction: rtl;
    display: flex;
    flex-direction: column;
    gap: 20px;
    overflow-y: auto;
    flex: 1;
    min-height: 0;
}
.form-row {
    display: flex;
    gap: 16px;
    margin-bottom: 0;
}
.form-row.single {
    flex-direction: column;
}
.form-row.double {
    flex-direction: row;
}
.form-row.double .form-group {
    flex: 1;
    min-width: 0;
}
.form-group.full-width {
    grid-column: 1 / -1;
}






/* Table Styles */
.activities-table-container {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    overflow: hidden;
}
.table-header {
    padding: 24px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}
.search-container {
    margin-bottom: 24px;
    display: flex;
    justify-content: center;
}
.search-box {
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;
    max-width: 500px;
    padding: 8px 12px;
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.05);
    transition: border-color 0.3s ease, background-color 0.3s ease, box-shadow 0.3s ease;
}
.search-input {
    flex: 1 1 auto;
    min-width: 0;
    width: 100%;
    padding: 8px 0;
    border: none;
    outline: none;
    background: transparent;
    color: white;
    font-size: 16px;
    direction: rtl;
    text-align: right;
    box-sizing: border-box;
}
.search-input::-moz-placeholder {
    color: rgba(255, 255, 255, 0.6);
    direction: rtl;
    text-align: right;
}
.search-input::placeholder {
    color: rgba(255, 255, 255, 0.6);
    direction: rtl;
    text-align: right;
}
.search-box:focus-within {
    border-color: #4f46e5;
    background: rgba(255, 255, 255, 0.08);
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.15);
}
.search-icon {
    position: static;
    color: rgba(255, 255, 255, 0.7);
    cursor: pointer;
    flex: 0 0 auto;
}
.clear-search-btn {
    position: absolute;
    left: 24px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.6);
    cursor: pointer;
    padding: 6px;
    border-radius: 6px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
}
.table-stats {
    display: flex;
    gap: 32px;
    justify-content: center;
    flex-wrap: wrap;
}
.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
}
.stat-item.clickable {
    cursor: pointer;
    padding: 12px 16px;
    border-radius: 12px;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}
.stat-item.clickable:hover {
    background: rgba(79, 70, 229, 0.1);
    border-color: rgba(79, 70, 229, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(79, 70, 229, 0.2);
}
.stat-item.selected {
    background: rgba(79, 70, 229, 0.2);
    border-color: #4f46e5;
    box-shadow: 0 4px 20px rgba(79, 70, 229, 0.3);
}
.stat-item.selected .stat-number {
    color: #6366f1;
}
.stat-item.selected .stat-label {
    color: #c7d2fe;
}
.stat-number {
    font-size: 32px;
    font-weight: 700;
    color: #4f46e5;
    line-height: 1;
}
.stat-label {
    font-size: 14px;
    color: #8892b0;
    font-weight: 500;
}
.table-wrapper {
    overflow-x: auto;
}
.activities-table {
    width: 100%;
    border-collapse: collapse;
    direction: rtl;
}
.activities-table th {
    background: rgba(255, 255, 255, 0.08);
    padding: 16px 12px;
    text-align: right;
    font-weight: 600;
    color: #e6f1ff;
    font-size: 14px;
    border-bottom: 2px solid rgba(255, 255, 255, 0.1);
    white-space: nowrap;
}
.activities-table td {
    padding: 16px 12px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    vertical-align: middle;
}
.activity-row {
    transition: all 0.3s ease;
}
.activity-row:hover {
    background: rgba(255, 255, 255, 0.05);
}

/* Column Specific Styles */
.col-title {
    min-width: 250px;
}
.activity-title h4 {
    margin: 0 0 4px 0;
    color: #e6f1ff;
    font-size: 16px;
    font-weight: 600;
    line-height: 1.3;
}
.activity-description {
    margin: 0;
    color: #8892b0;
    font-size: 13px;
    line-height: 1.4;
    display: -webkit-box;
    line-clamp: 2;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
.owner-info {
    display: flex;
    align-items: center;
}
.date-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2px;
}
.date-main {
    color: #e6f1ff;
    font-weight: 600;
    font-size: 14px;
}
.date-year {
    color: #8892b0;
    font-size: 12px;
}
.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    white-space: nowrap;
}
.status-indicator {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: currentColor;
}

/* Status Colors */
.status-executed-paid {
    background: rgba(34, 197, 94, 0.2);
    color: #22c55e;
    border: 1px solid rgba(34, 197, 94, 0.3);
}
.status-executed-unpaid {
    background: rgba(59, 130, 246, 0.2);
    color: #3b82f6;
    border: 1px solid rgba(59, 130, 246, 0.3);
}
.status-accepted {
    background: rgba(16, 185, 129, 0.2);
    color: #10b981;
    border: 1px solid rgba(16, 185, 129, 0.3);
}
.status-rejected {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
    border: 1px solid rgba(239, 68, 68, 0.3);
}
.status-needs-edit {
    background: rgba(245, 158, 11, 0.2);
    color: #f59e0b;
    border: 1px solid rgba(245, 158, 11, 0.3);
}
.status-paid-not-executed {
    background: rgba(168, 85, 247, 0.2);
    color: #a855f7;
    border: 1px solid rgba(168, 85, 247, 0.3);
}
.status-accepted-unpaid {
    background: rgba(6, 182, 212, 0.2);
    color: #06b6d4;
    border: 1px solid rgba(6, 182, 212, 0.3);
}
.status-sent {
    background: rgba(139, 92, 246, 0.2);
    color: #8b5cf6;
    border: 1px solid rgba(139, 92, 246, 0.3);
}
.governorate-name, .coordinator-name {
    color: #ccd6f6;
    font-weight: 500;
}
.action-buttons {
    display: flex;
    gap: 8px;
    justify-content: center;
}
.action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}
.action-btn.edit-btn {
    background: rgba(59, 130, 246, 0.2);
    color: #3b82f6;
    border: 1px solid rgba(59, 130, 246, 0.3);
}
.action-btn.edit-btn:hover {
    background: rgba(59, 130, 246, 0.3);
    transform: scale(1.1);
}
.action-btn.delete-btn {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
    border: 1px solid rgba(239, 68, 68, 0.3);
}
.action-btn.delete-btn:hover {
    background: rgba(239, 68, 68, 0.3);
    transform: scale(1.1);
}

/* File Download Styles */
.files-container {
    display: flex;
    flex-direction: column;
    gap: 4px;
    min-width: 120px;
}
.file-buttons {
    display: flex;
    flex-direction: column;
    gap: 4px;
}
.file-download-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 6px 10px;
    background: rgba(34, 197, 94, 0.15);
    border: 1px solid rgba(34, 197, 94, 0.3);
    border-radius: 6px;
    color: #22c55e;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    min-height: 28px;
    max-width: 140px;
}
.file-download-btn:hover {
    background: rgba(34, 197, 94, 0.25);
    border-color: rgba(34, 197, 94, 0.5);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(34, 197, 94, 0.2);
}
.file-download-btn svg {
    flex-shrink: 0;
    color: #22c55e;
}
.file-name {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex: 1;
    min-width: 0;
}
.no-files {
    color: #64748b;
    font-size: 12px;
    font-style: italic;
    text-align: center;
    padding: 8px;
}
@keyframes fadeIn {
from { opacity: 0;
}
to { opacity: 1;
}
}
@keyframes slideUp {
from {
        opacity: 0;
        transform: translateY(30px);
}
to {
        opacity: 1;
        transform: translateY(0);
}
}

/* Edit Form Styles */
.edit-form {
    direction: rtl;
}
.form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 0;
}
.form-group label {
    color: #cbd5e1;
    font-weight: 500;
    font-size: 14px;
    margin-bottom: 0;
    text-align: right;
}
.form-input, .form-textarea, .form-select {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #475569;
    border-radius: 6px;
    background: #334155;
    color: #f1f5f9;
    font-size: 14px;
    font-family: inherit;
    transition: all 0.2s ease;
    direction: rtl;
    text-align: right;
    box-sizing: border-box;
    outline: none;
}
.form-input:focus, .form-textarea:focus, .form-select:focus {
    border-color: #3b82f6;
    background: #1e293b;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}
.form-input::-moz-placeholder, .form-textarea::-moz-placeholder {
    color: #94a3b8;
}
.form-input::placeholder, .form-textarea::placeholder {
    color: #94a3b8;
}
.form-select {
    cursor: pointer;
    -webkit-appearance: none;
       -moz-appearance: none;
            appearance: none;
    background-image: url("data:image/svg+xml;charset=US-ASCII,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 4 5\"><path fill=\"%23f1f5f9\" d=\"M2 0L0 2h4zm0 5L0 3h4z\"/></svg>");
    background-repeat: no-repeat;
    background-position: left 12px center;
    background-size: 12px;
    padding-left: 32px;
}
.form-group.full-width {
    grid-column: 1 / -1;
}
.form-textarea {
    min-height: 100px;
    resize: vertical;
    line-height: 1.5;
}
.form-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    margin-top: 24px;
    padding-top: 20px;
    border-top: 1px solid #475569;
}
.save-btn, .cancel-btn {
    padding: 10px 20px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid transparent;
    outline: none;
}
.save-btn {
    background: #3b82f6;
    color: white;
    border-color: #3b82f6;
}
.save-btn:hover {
    background: #2563eb;
    border-color: #2563eb;
}
.cancel-btn {
    background: transparent;
    color: #94a3b8;
    border-color: #475569;
}
.cancel-btn:hover {
    background: #374151;
    border-color: #6b7280;
}

    /* Responsive Design for My Activities */
@media (max-width: 768px) {
.view-activities {
            padding: 0 4px;
}
.view-header {
            flex-direction: column;
            gap: 16px;
            padding: 24px 16px;
            margin-bottom: 24px;
}
.view-header-content {
            order: 1;
}
.refresh-btn {
            order: 2;
            align-self: center;
            min-width: 120px;
            padding: 10px 16px;
            font-size: 13px;
}
.view-title {
            font-size: 24px;
}
.view-description {
            font-size: 14px;
}
.activities-grid {
            grid-template-columns: 1fr;
            gap: 16px;
            padding: 0;
}
.activity-card {
            padding: 20px;
            margin: 0;
}
.activity-header {
            flex-direction: column;
            gap: 12px;
            align-items: stretch;
}
.activity-header h4 {
            font-size: 18px;
            margin-right: 0;
}
.activity-actions {
            justify-content: flex-end;
            gap: 8px;
}
.edit-btn, .delete-btn {
            padding: 8px 12px;
            font-size: 13px;
            min-width: 96px;
            height: 40px;
}
.activity-details p {
            font-size: 14px;
            flex-direction: column;
            align-items: flex-start;
            gap: 4px;
}
.activity-details strong {
            min-width: auto;
}
.no-activities-message, .loading-message {
            padding: 32px 16px;
            font-size: 16px;
}
}
@media (max-width: 480px) {
.activity-card {
            padding: 12px;
}
.activity-header h4 {
            font-size: 16px;
}
.activity-details p {
            font-size: 13px;
}
.edit-btn, .delete-btn {
            padding: 5px 10px;
            font-size: 11px;
}
.edit-modal {
            width: 95%;
            max-width: none;
            margin: 20px;
            border-radius: 16px;
}
.edit-modal-header {
            padding: 20px 24px;
}
.edit-form {
            padding: 24px;
            gap: 20px;
}
.form-row {
            grid-template-columns: 1fr;
            gap: 20px;
            padding: 12px;
            margin-bottom: 16px;
            border-radius: 16px;
}
.form-row:hover {
            transform: none;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}
.form-input, .form-textarea, .form-select {
            padding: 16px 20px;
            font-size: 16px;
            border-radius: 14px;
}
.form-textarea {
            min-height: 100px;
            padding-top: 18px;
            padding-bottom: 18px;
}
.form-group label {
            font-size: 13px;
            margin-right: 2px;
}
.form-select {
            padding-left: 35px;
            background-position: calc(100% - 18px) calc(1em + 2px), 
                                 calc(100% - 13px) calc(1em + 2px);
}
.form-actions {
            flex-direction: column-reverse;
            align-items: stretch;
            gap: 12px;
            padding: 20px 0 0 0;
}
.save-btn, .cancel-btn {
            width: 100%;
            padding: 14px;
            min-width: auto;
}
}

/* Modal Overlay Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(5px);
}
.modal-content {
    background: #1a1a2e;
    border-radius: 20px;
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
    animation: modalSlideIn 0.3s ease;
    border: 2px solid #4facfe;
    position: relative;
    z-index: 1001;
    padding: 10px;
}
@keyframes modalSlideIn {
from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
}
to {
        opacity: 1;
        transform: translateY(0) scale(1);
}
}

/* Modal Header Styles */
.modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 20px 20px 0 0;
}
.modal-header h3 {
    margin: 0;
    font-size: 1.3rem;
}
.close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.3s ease;
}
.close-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* Account Settings Modal Styles */
.account-settings-modal {
    max-width: 600px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    padding: 10px;
}
.account-form {
    padding: 20px 0;
}
.form-group {
    margin-bottom: 20px;
}
.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #f5f5f5;
    font-size: 14px;
}
.form-input {
    width: 100%;
    padding: 12px 16px;
    background: #2d3748;
    border: 2px solid #4a5568;
    border-radius: 8px;
    color: #f5f5f5;
    font-size: 14px;
    transition: all 0.3s ease;
    box-sizing: border-box;
}
.form-input:focus {
    outline: none;
    border-color: #4facfe;
    box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
    background: #374151;
}
.form-input::-moz-placeholder {
    color: #a0aec0;
}
.form-input::placeholder {
    color: #a0aec0;
}
.password-section {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #4a5568;
}
.password-section h4 {
    margin: 0 0 20px 0;
    color: #4facfe;
    font-size: 16px;
    font-weight: 600;
}
.form-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #4a5568;
}
.save-btn {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.3s ease;
    min-width: 120px;
}
.save-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(79, 172, 254, 0.4);
}
.save-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}
.cancel-btn {
    background: transparent;
    color: #a0aec0;
    border: 2px solid #4a5568;
    padding: 12px 24px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.3s ease;
    min-width: 120px;
}
.cancel-btn:hover {
    background: #374151;
    border-color: #6b7280;
    color: #f5f5f5;
}

/* Responsive styles for account modal */
@media (max-width: 768px) {
.account-settings-modal {
        width: 95%;
        margin: 20px;
}
.form-actions {
        flex-direction: column;
}
.save-btn,
    .cancel-btn {
        width: 100%;
}
}

/* Clickable Row Styles */
.clickable-row {
    cursor: pointer;
    transition: all 0.3s ease;
}
.clickable-row:hover {
    background-color: rgba(74, 85, 104, 0.1);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
.clickable-row:active {
    transform: translateY(0);
}

/* PIN Confirmation Modal Styles */
.pin-modal {
    max-width: 450px;
    background: linear-gradient(135deg, #1a1a2e, #16213e);
    border: 2px solid #4a5568;
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.6);
}
.pin-modal .modal-header {
    background: linear-gradient(135deg, #2d3748, #4a5568);
    border-bottom: 2px solid #4a5568;
    border-radius: 14px 14px 0 0;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.pin-modal .modal-header h3 {
    color: #4fc3f7;
    margin: 0;
    font-size: 1.4rem;
    font-weight: 600;
}
.pin-modal .modal-body {
    padding: 25px;
}
.pin-message {
    color: #e2e8f0;
    font-size: 1.1rem;
    margin-bottom: 20px;
    text-align: center;
    line-height: 1.6;
}
.pin-modal .form-group {
    margin-bottom: 0;
}
.pin-modal .form-group label {
    color: #4fc3f7;
    font-weight: 600;
    margin-bottom: 8px;
    display: block;
}
.pin-modal .form-control {
    background: #2d3748;
    border: 2px solid #4a5568;
    color: #e2e8f0;
    padding: 12px 16px;
    border-radius: 8px;
    font-size: 1rem;
    width: 100%;
    box-sizing: border-box;
    transition: all 0.3s ease;
}
.pin-modal .form-control:focus {
    border-color: #4fc3f7;
    box-shadow: 0 0 0 3px rgba(79, 195, 247, 0.1);
    outline: none;
}
.pin-modal .modal-footer {
    padding: 20px 25px;
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    border-top: 1px solid #4a5568;
}
.confirm-btn {
    background: linear-gradient(135deg, #e53e3e, #c53030);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 100px;
}
.confirm-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, #c53030, #9c2626);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(229, 62, 62, 0.4);
}
.confirm-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}
.pin-modal .cancel-btn {
    background: transparent;
    color: #a0aec0;
    border: 2px solid #4a5568;
    padding: 10px 20px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 100px;
}
.pin-modal .cancel-btn:hover {
    background: #4a5568;
    color: #e2e8f0;
    transform: translateY(-2px);
}

/* Activity Details Modal Styles */
.activity-details-modal {
    max-width: 900px;
    width: 95%;
    max-height: 90vh;
    background: #ffffff;
    color: #333333;
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.6);
    overflow: hidden;
    position: relative;
    z-index: 1001;
}

/* Enhanced modal overlay to prevent background scrolling */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    overflow: hidden;
}
.activity-details-modal .modal-header {
    background: linear-gradient(135deg, #1a1a2e, #16213e);
    color: #ffffff;
    padding: 20px 30px;
    border-bottom: 2px solid #4a5568;
}
.activity-details-modal .modal-header h3 {
    margin: 0;
    font-size: 24px;
    font-weight: 700;
}
.activity-details-modal .modal-body {
    padding: 0;
    max-height: calc(90vh - 140px);
    overflow-y: auto;
}
.activity-document {
    padding: 30px;
    font-family: 'Arial', sans-serif;
    line-height: 1.6;
}
.document-header {
    text-align: center;
    margin-bottom: 40px;
    border-bottom: 3px solid #1a1a2e;
    padding-bottom: 30px;
}
.logo-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}
.logo-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}
.organization-logo {
    width: 80px;
    height: 80px;
    -o-object-fit: contain;
       object-fit: contain;
    border-radius: 8px;
    border: 2px solid #1a1a2e;
    background: #ffffff;
    padding: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
.logo-label {
    font-size: 12px;
    font-weight: bold;
    color: #1a1a2e;
    text-align: center;
}
.organization-info h2 {
    margin: 0;
    font-size: 28px;
    font-weight: bold;
    color: #1a1a2e;
}
.organization-info h3 {
    margin: 5px 0 0 0;
    font-size: 20px;
    font-weight: 600;
    color: #4a5568;
}
.document-title h1 {
    margin: 0;
    font-size: 32px;
    font-weight: bold;
    color: #1a1a2e;
    text-decoration: underline;
}
.details-table {
    width: 100%;
}
.detail-row {
    display: flex;
    margin-bottom: 20px;
    border-bottom: 1px solid #e2e8f0;
    padding-bottom: 15px;
}
.detail-label {
    flex: 0 0 200px;
    font-weight: bold;
    color: #1a1a2e;
    padding-right: 20px;
    font-size: 16px;
}
.detail-value {
    flex: 1;
    color: #4a5568;
    font-size: 16px;
}
.goals-list, .target-groups-list, .levels-list {
    margin: 0;
    padding-right: 20px;
}
.goals-list li, .target-groups-list li, .levels-list li {
    margin-bottom: 8px;
    color: #4a5568;
}
.budget-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
    border: 2px solid #1a1a2e;
}
.budget-table th,
.budget-table td {
    border: 1px solid #cbd5e0;
    padding: 12px;
    text-align: center;
}
.budget-table th {
    background: #1a1a2e;
    color: #ffffff;
    font-weight: bold;
}
.budget-table tbody tr:nth-child(even) {
    background: #f8f9fa;
}
.budget-table .total-row {
    background: #e2e8f0 !important;
    font-weight: bold;
}
.status-badge {
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: bold;
    font-size: 14px;
}
.activity-details-modal .modal-footer {
    background: #f8f9fa;
    padding: 20px 30px;
    border-top: 1px solid #e2e8f0;
    text-align: center;
}
.close-modal-btn {
    background: linear-gradient(135deg, #1a1a2e, #16213e);
    color: #ffffff;
    border: none;
    padding: 12px 30px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-left: 10px;
}
.close-modal-btn:hover {
    background: linear-gradient(135deg, #16213e, #1a1a2e);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}
.export-docx-btn {
    background: linear-gradient(135deg, #2563eb, #1d4ed8);
    color: #ffffff;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}
.export-docx-btn:hover {
    background: linear-gradient(135deg, #1d4ed8, #1e40af);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.4);
}
.export-docx-btn:active {
    transform: translateY(0);
}
.print-btn {
    background: linear-gradient(135deg, #059669, #047857);
    color: #ffffff;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
}
.print-btn:hover {
    background: linear-gradient(135deg, #047857, #065f46);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(5, 150, 105, 0.4);
}
.print-btn:active {
    transform: translateY(0);
}

/* Responsive styles for modal footer buttons */
@media (max-width: 768px) {
.activity-details-modal .modal-footer {
        flex-direction: column;
        gap: 10px;
}
.print-btn,
    .export-docx-btn,
    .close-modal-btn {
        width: 100%;
        margin: 0;
        justify-content: center;
}
.print-btn {
        margin-right: 0;
}
}
.activity-details-modal .modal-footer {
    background: #f8f9fa;
    padding: 20px 30px;
    border-top: 1px solid #e2e8f0;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
}

/* Responsive Design for Activity Modal */
@media (max-width: 768px) {
.activity-details-modal {
        width: 98%;
        max-height: 95vh;
}
.activity-document {
        padding: 20px;
}
.logo-section {
        flex-direction: column;
        gap: 15px;
}
.organization-logo {
        width: 60px;
        height: 60px;
}
.logo-label {
        font-size: 10px;
}
.detail-row {
        flex-direction: column;
}
.detail-label {
        flex: none;
        margin-bottom: 5px;
        padding-right: 0;
}
.budget-table {
        font-size: 12px;
}
.budget-table th,
    .budget-table td {
        padding: 8px 4px;
}
}


/* Global Styles */
[data-v-cdff2924] {
  box-sizing: border-box;
}
.login-container[data-v-cdff2924] {
  min-height: 100vh;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
  direction: rtl;
}

/* Animated Background */
.animated-bg[data-v-cdff2924] {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  overflow: hidden;
}
.floating-shapes[data-v-cdff2924] {
  position: relative;
  width: 100%;
  height: 100%;
}
.shape[data-v-cdff2924] {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(45deg, rgba(79, 70, 229, 0.1), rgba(124, 58, 237, 0.1));
  animation: float-cdff2924 6s ease-in-out infinite;
}
.shape-1[data-v-cdff2924] {
  width: 80px;
  height: 80px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}
.shape-2[data-v-cdff2924] {
  width: 120px;
  height: 120px;
  top: 20%;
  right: 15%;
  animation-delay: 2s;
}
.shape-3[data-v-cdff2924] {
  width: 60px;
  height: 60px;
  bottom: 30%;
  left: 20%;
  animation-delay: 4s;
}
.shape-4[data-v-cdff2924] {
  width: 100px;
  height: 100px;
  bottom: 20%;
  right: 10%;
  animation-delay: 1s;
}
.shape-5[data-v-cdff2924] {
  width: 140px;
  height: 140px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation-delay: 3s;
}
@keyframes float-cdff2924 {
0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.5;
}
50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 0.8;
}
}

/* Header Section */
.header-section[data-v-cdff2924] {
  position: relative;
  z-index: 2;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}
.header-content[data-v-cdff2924] {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2rem;
  flex-wrap: wrap;
}
.logo-container[data-v-cdff2924] {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}
.logo[data-v-cdff2924] {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  border: 3px solid rgba(79, 70, 229, 0.5);
  transition: all 0.3s ease;
  position: relative;
  z-index: 2;
}
.logo-glow[data-v-cdff2924] {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 90px;
  height: 90px;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(79, 70, 229, 0.3) 0%, transparent 70%);
  animation: pulse-cdff2924 2s ease-in-out infinite;
}
@keyframes pulse-cdff2924 {
0%, 100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.5;
}
50% {
    transform: translate(-50%, -50%) scale(1.1);
    opacity: 0.8;
}
}
.header-text[data-v-cdff2924] {
  text-align: center;
}
.org-name[data-v-cdff2924] {
  font-size: clamp(1.2rem, 4vw, 1.8rem);
  font-weight: 800;
  color: #f8fafc;
  margin: 0 0 0.5rem 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}
.team-name[data-v-cdff2924] {
  font-size: clamp(1rem, 3vw, 1.4rem);
  font-weight: 600;
  color: #cbd5e1;
  margin: 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* Main Content */
.main-content[data-v-cdff2924] {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: calc(100vh - 140px);
  padding: 2rem 1rem;
}
.login-card[data-v-cdff2924] {
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 24px;
  padding: 2.5rem;
  width: 100%;
  max-width: 480px;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.4),
    0 0 0 1px rgba(255, 255, 255, 0.05);
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
}
.login-card[data-v-cdff2924]::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(79, 70, 229, 0.5), transparent);
}
.login-card[data-v-cdff2924]:hover {
  transform: translateY(-8px);
  box-shadow:
    0 32px 64px rgba(0, 0, 0, 0.5),
    0 0 0 1px rgba(255, 255, 255, 0.1);
}

/* Card Header */
.card-header[data-v-cdff2924] {
  text-align: center;
  margin-bottom: 2.5rem;
}
.login-icon[data-v-cdff2924] {
  width: 64px;
  height: 64px;
  margin: 0 auto 1.5rem;
  background: linear-gradient(135deg, #4f46e5, #7c3aed);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 8px 24px rgba(79, 70, 229, 0.3);
  animation: iconFloat-cdff2924 3s ease-in-out infinite;
}
.login-icon svg[data-v-cdff2924] {
  width: 32px;
  height: 32px;
}
@keyframes iconFloat-cdff2924 {
0%, 100% {
    transform: translateY(0);
}
50% {
    transform: translateY(-4px);
}
}
.card-title[data-v-cdff2924] {
  font-size: 2rem;
  font-weight: 800;
  margin: 0 0 0.5rem 0;
  background: linear-gradient(135deg, #4f46e5, #7c3aed);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}
.card-subtitle[data-v-cdff2924] {
  color: #94a3b8;
  font-size: 1rem;
  margin: 0;
  font-weight: 500;
}

/* Form Styles */
.login-form[data-v-cdff2924] {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}
.input-group[data-v-cdff2924] {
  position: relative;
}
.input-wrapper[data-v-cdff2924] {
  position: relative;
  display: flex;
  align-items: center;
}
.input-icon[data-v-cdff2924] {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  color: #64748b;
  z-index: 2;
  transition: color 0.3s ease;
}
.form-input[data-v-cdff2924] {
  width: 100%;
  padding: 1rem 3rem 1rem 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  color: #f1f5f9;
  font-size: 1rem;
  transition: all 0.3s ease;
  text-align: right;
  direction: rtl;
}
.form-input[data-v-cdff2924]:focus {
  outline: none;
  border-color: #4f46e5;
  background: rgba(255, 255, 255, 0.1);
  box-shadow: 0 0 0 4px rgba(79, 70, 229, 0.1);
}
.form-input:not(:-moz-placeholder) + .floating-label[data-v-cdff2924] {
  transform: translateY(-2.5rem) scale(0.85);
  color: #ffffff;
  background: linear-gradient(to bottom, transparent 0%, rgba(15, 15, 35, 0.8) 20%, rgba(15, 15, 35, 0.8) 80%, transparent 100%);
}
.form-input:focus + .floating-label[data-v-cdff2924],
.form-input:not(:placeholder-shown) + .floating-label[data-v-cdff2924] {
  transform: translateY(-2.5rem) scale(0.85);
  color: #ffffff;
  background: linear-gradient(to bottom, transparent 0%, rgba(15, 15, 35, 0.8) 20%, rgba(15, 15, 35, 0.8) 80%, transparent 100%);
}
.form-input:focus ~ .input-icon[data-v-cdff2924] {
  color: #4f46e5;
}
.floating-label[data-v-cdff2924] {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #64748b;
  font-size: 1rem;
  font-weight: 500;
  transition: all 0.3s ease;
  pointer-events: none;
  padding: 0 0.5rem;
}

/* Button Styles */
.login-btn[data-v-cdff2924] {
  width: 100%;
  padding: 1rem 2rem;
  background: linear-gradient(135deg, #4f46e5, #7c3aed);
  border: none;
  border-radius: 16px;
  color: white;
  font-size: 1.1rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 8px 24px rgba(79, 70, 229, 0.3);
  margin-top: 1rem;
}
.login-btn[data-v-cdff2924]::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}
.login-btn[data-v-cdff2924]:hover::before {
  left: 100%;
}
.login-btn[data-v-cdff2924]:hover {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  transform: translateY(-2px);
  box-shadow: 0 12px 32px rgba(79, 70, 229, 0.4);
}
.login-btn[data-v-cdff2924]:active {
  transform: translateY(0);
}
.login-btn[data-v-cdff2924]:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}
.btn-content[data-v-cdff2924] {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}
.btn-icon[data-v-cdff2924] {
  width: 20px;
  height: 20px;
  transition: transform 0.3s ease;
}
.login-btn:hover .btn-icon[data-v-cdff2924] {
  transform: translateX(-4px);
}
.btn-loading[data-v-cdff2924] {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
}
.loading-spinner[data-v-cdff2924] {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin-cdff2924 1s linear infinite;
}
@keyframes spin-cdff2924 {
0% { transform: rotate(0deg);
}
100% { transform: rotate(360deg);
}
}

/* Error Message */
.error-message[data-v-cdff2924] {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: 12px;
  color: #fca5a5;
  font-size: 0.9rem;
  margin-top: 1rem;
  animation: slideIn-cdff2924 0.3s ease;
}
.error-message svg[data-v-cdff2924] {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}
@keyframes slideIn-cdff2924 {
from {
    opacity: 0;
    transform: translateY(-10px);
}
to {
    opacity: 1;
    transform: translateY(0);
}
}

/* Card Footer */
.card-footer[data-v-cdff2924] {
  margin-top: 2rem;
  text-align: center;
}
.register-link[data-v-cdff2924] {
  color: #94a3b8;
  font-size: 0.95rem;
  margin: 0;
}
.link[data-v-cdff2924] {
  color: #4f46e5;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
  position: relative;
}
.link[data-v-cdff2924]::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(135deg, #4f46e5, #7c3aed);
  transition: width 0.3s ease;
}
.link[data-v-cdff2924]:hover::after {
  width: 100%;
}
.link[data-v-cdff2924]:hover {
  color: #6366f1;
}

/* Responsive Design */
@media (max-width: 768px) {
.header-section[data-v-cdff2924] {
    padding: 1rem;
}
.header-content[data-v-cdff2924] {
    flex-direction: column;
    gap: 1rem;
}
.logo[data-v-cdff2924] {
    width: 60px;
    height: 60px;
}
.logo-glow[data-v-cdff2924] {
    width: 80px;
    height: 80px;
}
.main-content[data-v-cdff2924] {
    padding: 1.5rem 1rem;
    min-height: calc(100vh - 120px);
}
.login-card[data-v-cdff2924] {
    padding: 2rem;
    border-radius: 20px;
}
.card-title[data-v-cdff2924] {
    font-size: 1.75rem;
}
.login-icon[data-v-cdff2924] {
    width: 56px;
    height: 56px;
    margin-bottom: 1rem;
}
.login-icon svg[data-v-cdff2924] {
    width: 28px;
    height: 28px;
}
.form-input[data-v-cdff2924] {
    padding: 0.875rem 2.5rem 0.875rem 0.875rem;
    font-size: 16px; /* Prevents zoom on iOS */
}
.login-btn[data-v-cdff2924] {
    padding: 0.875rem 1.5rem;
    font-size: 1rem;
}
}
@media (max-width: 480px) {
.header-section[data-v-cdff2924] {
    padding: 0.75rem;
}
.logo[data-v-cdff2924] {
    width: 50px;
    height: 50px;
}
.logo-glow[data-v-cdff2924] {
    width: 70px;
    height: 70px;
}
.main-content[data-v-cdff2924] {
    padding: 1rem 0.75rem;
}
.login-card[data-v-cdff2924] {
    padding: 1.5rem;
    border-radius: 16px;
}
.card-title[data-v-cdff2924] {
    font-size: 1.5rem;
}
.card-subtitle[data-v-cdff2924] {
    font-size: 0.9rem;
}
.login-icon[data-v-cdff2924] {
    width: 48px;
    height: 48px;
    border-radius: 16px;
}
.login-icon svg[data-v-cdff2924] {
    width: 24px;
    height: 24px;
}
.login-form[data-v-cdff2924] {
    gap: 1.25rem;
}
.form-input[data-v-cdff2924] {
    padding: 0.75rem 2.25rem 0.75rem 0.75rem;
    border-radius: 12px;
}
.input-icon[data-v-cdff2924] {
    left: 0.75rem;
    width: 18px;
    height: 18px;
}
.floating-label[data-v-cdff2924] {
    right: 0.75rem;
    font-size: 0.9rem;
}
.login-btn[data-v-cdff2924] {
    padding: 0.75rem 1.25rem;
    border-radius: 12px;
}
.btn-icon[data-v-cdff2924] {
    width: 18px;
    height: 18px;
}
.error-message[data-v-cdff2924] {
    padding: 0.75rem;
    font-size: 0.85rem;
}
.register-link[data-v-cdff2924] {
    font-size: 0.9rem;
}
}
@media (max-width: 360px) {
.login-card[data-v-cdff2924] {
    padding: 1.25rem;
}
.card-header[data-v-cdff2924] {
    margin-bottom: 2rem;
}
.card-title[data-v-cdff2924] {
    font-size: 1.375rem;
}
.form-input[data-v-cdff2924] {
    font-size: 14px;
}
.floating-label[data-v-cdff2924] {
    font-size: 0.85rem;
}
}


/* Global Styles */
[data-v-eeea6110] {
  box-sizing: border-box;
}
.register-container[data-v-eeea6110] {
  min-height: 100vh;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
  direction: rtl;
}

/* Animated Background */
.animated-bg[data-v-eeea6110] {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  overflow: hidden;
}
.floating-shapes[data-v-eeea6110] {
  position: relative;
  width: 100%;
  height: 100%;
}
.shape[data-v-eeea6110] {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(45deg, rgba(79, 70, 229, 0.08), rgba(124, 58, 237, 0.08));
  animation: float-eeea6110 8s ease-in-out infinite;
}
.shape-1[data-v-eeea6110] {
  width: 100px;
  height: 100px;
  top: 15%;
  left: 8%;
  animation-delay: 0s;
}
.shape-2[data-v-eeea6110] {
  width: 140px;
  height: 140px;
  top: 25%;
  right: 12%;
  animation-delay: 2.5s;
}
.shape-3[data-v-eeea6110] {
  width: 80px;
  height: 80px;
  bottom: 35%;
  left: 15%;
  animation-delay: 5s;
}
.shape-4[data-v-eeea6110] {
  width: 120px;
  height: 120px;
  bottom: 15%;
  right: 8%;
  animation-delay: 1.5s;
}
.shape-5[data-v-eeea6110] {
  width: 160px;
  height: 160px;
  top: 45%;
  left: 45%;
  transform: translate(-50%, -50%);
  animation-delay: 3.5s;
}
.shape-6[data-v-eeea6110] {
  width: 90px;
  height: 90px;
  top: 8%;
  left: 50%;
  transform: translateX(-50%);
  animation-delay: 4.5s;
}
@keyframes float-eeea6110 {
0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.4;
}
50% {
    transform: translateY(-25px) rotate(180deg);
    opacity: 0.7;
}
}

/* Header Section */
.header-section[data-v-eeea6110] {
  position: relative;
  z-index: 2;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}
.header-content[data-v-eeea6110] {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2rem;
  flex-wrap: wrap;
}
.logo-container[data-v-eeea6110] {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}
.logo[data-v-eeea6110] {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  border: 3px solid rgba(79, 70, 229, 0.5);
  transition: all 0.3s ease;
  position: relative;
  z-index: 2;
}
.logo-glow[data-v-eeea6110] {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 90px;
  height: 90px;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(79, 70, 229, 0.3) 0%, transparent 70%);
  animation: pulse-eeea6110 2s ease-in-out infinite;
}
@keyframes pulse-eeea6110 {
0%, 100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.5;
}
50% {
    transform: translate(-50%, -50%) scale(1.1);
    opacity: 0.8;
}
}
.header-text[data-v-eeea6110] {
  text-align: center;
}
.org-name[data-v-eeea6110] {
  font-size: clamp(1.2rem, 4vw, 1.8rem);
  font-weight: 800;
  color: #f8fafc;
  margin: 0 0 0.5rem 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}
.team-name[data-v-eeea6110] {
  font-size: clamp(1rem, 3vw, 1.4rem);
  font-weight: 600;
  color: #cbd5e1;
  margin: 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* Main Content */
.main-content[data-v-eeea6110] {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: calc(100vh - 140px);
  padding: 2rem 1rem;
}
.register-card[data-v-eeea6110] {
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 24px;
  padding: 2.5rem;
  width: 100%;
  max-width: 600px;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.4),
    0 0 0 1px rgba(255, 255, 255, 0.05);
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
}
.register-card[data-v-eeea6110]::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(79, 70, 229, 0.5), transparent);
}
.register-card[data-v-eeea6110]:hover {
  transform: translateY(-8px);
  box-shadow:
    0 32px 64px rgba(0, 0, 0, 0.5),
    0 0 0 1px rgba(255, 255, 255, 0.1);
}

/* Card Header */
.card-header[data-v-eeea6110] {
  text-align: center;
  margin-bottom: 2.5rem;
}
.register-icon[data-v-eeea6110] {
  width: 64px;
  height: 64px;
  margin: 0 auto 1.5rem;
  background: linear-gradient(135deg, #10b981, #059669);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 8px 24px rgba(16, 185, 129, 0.3);
  animation: iconFloat-eeea6110 3s ease-in-out infinite;
}
.register-icon svg[data-v-eeea6110] {
  width: 32px;
  height: 32px;
}
@keyframes iconFloat-eeea6110 {
0%, 100% {
    transform: translateY(0);
}
50% {
    transform: translateY(-4px);
}
}
.card-title[data-v-eeea6110] {
  font-size: 2rem;
  font-weight: 800;
  margin: 0 0 0.5rem 0;
  background: linear-gradient(135deg, #10b981, #059669);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}
.card-subtitle[data-v-eeea6110] {
  color: #94a3b8;
  font-size: 1rem;
  margin: 0;
  font-weight: 500;
}

/* Form Styles */
.register-form[data-v-eeea6110] {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}
.form-row[data-v-eeea6110] {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}
.input-group[data-v-eeea6110] {
  position: relative;
}
.input-wrapper[data-v-eeea6110], .select-wrapper[data-v-eeea6110] {
  position: relative;
  display: flex;
  align-items: center;
}
.input-icon[data-v-eeea6110] {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  color: #64748b;
  z-index: 2;
  transition: color 0.3s ease;
}
.form-input[data-v-eeea6110], .form-select[data-v-eeea6110] {
  width: 100%;
  padding: 1rem 3rem 1rem 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  color: #f1f5f9;
  font-size: 1rem;
  transition: all 0.3s ease;
  text-align: right;
  direction: rtl;
}
.form-select[data-v-eeea6110] {
  cursor: pointer;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
}
.form-input[data-v-eeea6110]:focus, .form-select[data-v-eeea6110]:focus {
  outline: none;
  border-color: #10b981;
  background: rgba(255, 255, 255, 0.1);
  box-shadow: 0 0 0 4px rgba(16, 185, 129, 0.1);
}
.form-input:not(:-moz-placeholder) + .floating-label[data-v-eeea6110] {
  transform: translateY(-2.5rem) scale(0.85);
  color: #10b981;
  background: linear-gradient(to bottom, transparent 0%, rgba(15, 15, 35, 0.8) 20%, rgba(15, 15, 35, 0.8) 80%, transparent 100%);
}
.form-input:focus + .floating-label[data-v-eeea6110],
.form-input:not(:placeholder-shown) + .floating-label[data-v-eeea6110],
.form-select:focus + .floating-label[data-v-eeea6110],
.form-select:not([value=""]) + .floating-label[data-v-eeea6110] {
  transform: translateY(-2.5rem) scale(0.85);
  color: #10b981;
  background: linear-gradient(to bottom, transparent 0%, rgba(15, 15, 35, 0.8) 20%, rgba(15, 15, 35, 0.8) 80%, transparent 100%);
}
.form-input:focus ~ .input-icon[data-v-eeea6110],
.form-select:focus ~ .input-icon[data-v-eeea6110] {
  color: #10b981;
}
.floating-label[data-v-eeea6110] {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #64748b;
  font-size: 1rem;
  font-weight: 500;
  transition: all 0.3s ease;
  pointer-events: none;
  padding: 0 0.5rem;
}
.select-label[data-v-eeea6110] {
  z-index: 1;
}
.select-arrow[data-v-eeea6110] {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  color: #64748b;
  pointer-events: none;
  transition: color 0.3s ease;
}
.form-select:focus ~ .select-arrow[data-v-eeea6110] {
  color: #10b981;
}
.form-select option[data-v-eeea6110] {
  background: #1b1f24;
  color: #e5e7eb;
  padding: 0.5rem;
}

/* Button Styles */
.register-btn[data-v-eeea6110] {
  width: 100%;
  padding: 1rem 2rem;
  background: linear-gradient(135deg, #10b981, #059669);
  border: none;
  border-radius: 16px;
  color: white;
  font-size: 1.1rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 8px 24px rgba(16, 185, 129, 0.3);
  margin-top: 1rem;
}
.register-btn[data-v-eeea6110]::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}
.register-btn[data-v-eeea6110]:hover::before {
  left: 100%;
}
.register-btn[data-v-eeea6110]:hover {
  background: linear-gradient(135deg, #34d399, #10b981);
  transform: translateY(-2px);
  box-shadow: 0 12px 32px rgba(16, 185, 129, 0.4);
}
.register-btn[data-v-eeea6110]:active {
  transform: translateY(0);
}
.register-btn[data-v-eeea6110]:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}
.btn-content[data-v-eeea6110] {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}
.btn-icon[data-v-eeea6110] {
  width: 20px;
  height: 20px;
  transition: transform 0.3s ease;
}
.register-btn:hover .btn-icon[data-v-eeea6110] {
  transform: translateX(-4px);
}
.btn-loading[data-v-eeea6110] {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
}
.loading-spinner[data-v-eeea6110] {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin-eeea6110 1s linear infinite;
}
@keyframes spin-eeea6110 {
0% { transform: rotate(0deg);
}
100% { transform: rotate(360deg);
}
}

/* Error Message */
.error-message[data-v-eeea6110] {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: 12px;
  color: #fca5a5;
  font-size: 0.9rem;
  margin-top: 1rem;
  animation: slideIn-eeea6110 0.3s ease;
}
.error-message svg[data-v-eeea6110] {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}
@keyframes slideIn-eeea6110 {
from {
    opacity: 0;
    transform: translateY(-10px);
}
to {
    opacity: 1;
    transform: translateY(0);
}
}

/* Card Footer */
.card-footer[data-v-eeea6110] {
  margin-top: 2rem;
  text-align: center;
}
.login-link[data-v-eeea6110] {
  color: #94a3b8;
  font-size: 0.95rem;
  margin: 0;
}
.link[data-v-eeea6110] {
  color: #10b981;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
  position: relative;
}
.link[data-v-eeea6110]::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(135deg, #10b981, #059669);
  transition: width 0.3s ease;
}
.link[data-v-eeea6110]:hover::after {
  width: 100%;
}
.link[data-v-eeea6110]:hover {
  color: #34d399;
}

/* Responsive Design */
@media (max-width: 768px) {
.header-section[data-v-eeea6110] {
    padding: 1rem;
}
.header-content[data-v-eeea6110] {
    flex-direction: column;
    gap: 1rem;
}
.logo[data-v-eeea6110] {
    width: 60px;
    height: 60px;
}
.logo-glow[data-v-eeea6110] {
    width: 80px;
    height: 80px;
}
.main-content[data-v-eeea6110] {
    padding: 1.5rem 1rem;
    min-height: calc(100vh - 120px);
}
.register-card[data-v-eeea6110] {
    padding: 2rem;
    border-radius: 20px;
    max-width: 500px;
}
.card-title[data-v-eeea6110] {
    font-size: 1.75rem;
}
.register-icon[data-v-eeea6110] {
    width: 56px;
    height: 56px;
    margin-bottom: 1rem;
}
.register-icon svg[data-v-eeea6110] {
    width: 28px;
    height: 28px;
}
.form-row[data-v-eeea6110] {
    grid-template-columns: 1fr;
    gap: 1.5rem;
}
.form-input[data-v-eeea6110], .form-select[data-v-eeea6110] {
    padding: 0.875rem 2.5rem 0.875rem 0.875rem;
    font-size: 16px; /* Prevents zoom on iOS */
}
.register-btn[data-v-eeea6110] {
    padding: 0.875rem 1.5rem;
    font-size: 1rem;
}
}
@media (max-width: 480px) {
.header-section[data-v-eeea6110] {
    padding: 0.75rem;
}
.logo[data-v-eeea6110] {
    width: 50px;
    height: 50px;
}
.logo-glow[data-v-eeea6110] {
    width: 70px;
    height: 70px;
}
.main-content[data-v-eeea6110] {
    padding: 1rem 0.75rem;
}
.register-card[data-v-eeea6110] {
    padding: 1.5rem;
    border-radius: 16px;
}
.card-title[data-v-eeea6110] {
    font-size: 1.5rem;
}
.card-subtitle[data-v-eeea6110] {
    font-size: 0.9rem;
}
.register-icon[data-v-eeea6110] {
    width: 48px;
    height: 48px;
    border-radius: 16px;
}
.register-icon svg[data-v-eeea6110] {
    width: 24px;
    height: 24px;
}
.register-form[data-v-eeea6110] {
    gap: 1.25rem;
}
.form-input[data-v-eeea6110], .form-select[data-v-eeea6110] {
    padding: 0.75rem 2.25rem 0.75rem 0.75rem;
    border-radius: 12px;
}
.input-icon[data-v-eeea6110] {
    left: 0.75rem;
    width: 18px;
    height: 18px;
}
.floating-label[data-v-eeea6110] {
    right: 0.75rem;
    font-size: 0.9rem;
}
.select-arrow[data-v-eeea6110] {
    right: 0.75rem;
    width: 18px;
    height: 18px;
}
.register-btn[data-v-eeea6110] {
    padding: 0.75rem 1.25rem;
    border-radius: 12px;
}
.btn-icon[data-v-eeea6110] {
    width: 18px;
    height: 18px;
}
.error-message[data-v-eeea6110] {
    padding: 0.75rem;
    font-size: 0.85rem;
}
.login-link[data-v-eeea6110] {
    font-size: 0.9rem;
}
}
@media (max-width: 360px) {
.register-card[data-v-eeea6110] {
    padding: 1.25rem;
}
.card-header[data-v-eeea6110] {
    margin-bottom: 2rem;
}
.card-title[data-v-eeea6110] {
    font-size: 1.375rem;
}
.form-input[data-v-eeea6110], .form-select[data-v-eeea6110] {
    font-size: 14px;
}
.floating-label[data-v-eeea6110] {
    font-size: 0.85rem;
}
}


.admin-container[data-v-25c40afe] {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  direction: rtl;
}
.admin-header[data-v-25c40afe] {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 15px;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
  position: relative;
}
.header-top[data-v-25c40afe] {
  position: absolute;
  top: 20px;
  right: 20px;
}
.back-btn[data-v-25c40afe] {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 10px 16px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}
.back-btn[data-v-25c40afe]:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}
.back-btn svg[data-v-25c40afe] {
  transition: transform 0.3s ease;
}
.back-btn:hover svg[data-v-25c40afe] {
  transform: translateX(-2px);
}
.admin-header h1[data-v-25c40afe] {
  margin: 0 0 10px 0;
  font-size: 2.5rem;
  font-weight: 700;
}
.admin-subtitle[data-v-25c40afe] {
  margin: 0;
  font-size: 1.1rem;
  opacity: 0.9;
}
.loading-container[data-v-25c40afe], .error-container[data-v-25c40afe] {
  text-align: center;
  padding: 60px 20px;
}
.loading-spinner[data-v-25c40afe] {
  width: 50px;
  height: 50px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin-25c40afe 1s linear infinite;
  margin: 0 auto 20px;
}
@keyframes spin-25c40afe {
0% { transform: rotate(0deg);
}
100% { transform: rotate(360deg);
}
}
.error-message[data-v-25c40afe] {
  color: #e74c3c;
  font-size: 1.1rem;
  margin-bottom: 20px;
}
.retry-btn[data-v-25c40afe] {
  background: #667eea;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s ease;
}
.retry-btn[data-v-25c40afe]:hover {
  background: #5a6fd8;
  transform: translateY(-2px);
}
.users-table-container[data-v-25c40afe] {
  background: #1a1a2e;
  border-radius: 15px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  border: 1px solid #16213e;
}
.table-header[data-v-25c40afe] {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
  padding: 20px;
  text-align: center;
}
.table-header h2[data-v-25c40afe] {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}
.table-wrapper[data-v-25c40afe] {
  overflow-x: auto;
}
.users-table[data-v-25c40afe] {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.95rem;
}
.users-table th[data-v-25c40afe] {
  background: #0f3460;
  padding: 15px 12px;
  text-align: right;
  font-weight: 600;
  color: #e2e8f0;
  border-bottom: 2px solid #16213e;
  position: sticky;
  top: 0;
  z-index: 10;
}
.users-table td[data-v-25c40afe] {
  padding: 15px 12px;
  border-bottom: 1px solid #16213e;
  vertical-align: middle;
  color: #cbd5e0;
  background: #1a1a2e;
}
.user-row[data-v-25c40afe] {
  transition: all 0.3s ease;
}
.user-row[data-v-25c40afe]:hover {
  background: linear-gradient(135deg, #667eea20, #764ba220);
  transform: scale(1.01);
}
.username[data-v-25c40afe] {
  font-weight: 600;
  color: #4facfe;
}
.rank-badge[data-v-25c40afe] {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 600;
  text-align: center;
  display: inline-block;
  min-width: 60px;
}
.rank-member[data-v-25c40afe] {
  background: #e3f2fd;
  color: #1976d2;
}
.rank-leader[data-v-25c40afe] {
  background: #fff3e0;
  color: #f57c00;
}
.rank-admin[data-v-25c40afe] {
  background: #ffebee;
  color: #d32f2f;
}
.edit-btn[data-v-25c40afe] {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.3s ease;
}
.edit-btn[data-v-25c40afe]:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(79, 172, 254, 0.4);
}
.modal-overlay[data-v-25c40afe] {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}
.modal-content[data-v-25c40afe] {
  background: #1a1a2e;
  border-radius: 20px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
  animation: modalSlideIn-25c40afe 0.3s ease;
  border: 1px solid #16213e;
}
@keyframes modalSlideIn-25c40afe {
from {
    opacity: 0;
    transform: translateY(-50px) scale(0.9);
}
to {
    opacity: 1;
    transform: translateY(0) scale(1);
}
}
.modal-header[data-v-25c40afe] {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 20px 20px 0 0;
}
.modal-header h3[data-v-25c40afe] {
  margin: 0;
  font-size: 1.3rem;
}
.close-btn[data-v-25c40afe] {
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.3s ease;
}
.close-btn[data-v-25c40afe]:hover {
  background: rgba(255, 255, 255, 0.2);
}
.edit-form[data-v-25c40afe] {
  padding: 30px;
}
.form-group[data-v-25c40afe] {
  margin-bottom: 20px;
}
.form-group label[data-v-25c40afe] {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #e2e8f0;
}
.form-select[data-v-25c40afe] {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #16213e;
  border-radius: 10px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: #0f3460;
  color: #e2e8f0;
}
.form-select[data-v-25c40afe]:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.3);
}
.form-actions[data-v-25c40afe] {
  display: flex;
  gap: 15px;
  justify-content: flex-end;
  margin-top: 30px;
}
.cancel-btn[data-v-25c40afe] {
  background: #6c757d;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 10px;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s ease;
}
.cancel-btn[data-v-25c40afe]:hover {
  background: #5a6268;
  transform: translateY(-2px);
}
.save-btn[data-v-25c40afe] {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 10px;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s ease;
  min-width: 120px;
}
.save-btn[data-v-25c40afe]:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}
.save-btn[data-v-25c40afe]:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}
@media (max-width: 768px) {
.admin-container[data-v-25c40afe] {
    padding: 10px;
}
.admin-header h1[data-v-25c40afe] {
    font-size: 2rem;
}
.users-table[data-v-25c40afe] {
    font-size: 0.85rem;
}
.users-table th[data-v-25c40afe],
  .users-table td[data-v-25c40afe] {
    padding: 10px 8px;
}
.modal-content[data-v-25c40afe] {
    width: 95%;
    margin: 10px;
}
.edit-form[data-v-25c40afe] {
    padding: 20px;
}
.form-actions[data-v-25c40afe] {
    flex-direction: column;
}
}

/* sass-plugin-1:/Users/<USER>/Developer/vue-toastification/src/scss/index.scss */
.Vue-Toastification__container {
  z-index: 9999;
  position: fixed;
  padding: 4px;
  width: 600px;
  box-sizing: border-box;
  display: flex;
  min-height: 100%;
  color: #fff;
  flex-direction: column;
  pointer-events: none;
}
@media only screen and (min-width : 600px) {
  .Vue-Toastification__container.top-left,
  .Vue-Toastification__container.top-right,
  .Vue-Toastification__container.top-center {
    top: 1em;
  }
  .Vue-Toastification__container.bottom-left,
  .Vue-Toastification__container.bottom-right,
  .Vue-Toastification__container.bottom-center {
    bottom: 1em;
    flex-direction: column-reverse;
  }
  .Vue-Toastification__container.top-left,
  .Vue-Toastification__container.bottom-left {
    left: 1em;
  }
  .Vue-Toastification__container.top-left .Vue-Toastification__toast,
  .Vue-Toastification__container.bottom-left .Vue-Toastification__toast {
    margin-right: auto;
  }
  @supports not (-moz-appearance: none) {
    .Vue-Toastification__container.top-left .Vue-Toastification__toast--rtl,
    .Vue-Toastification__container.bottom-left .Vue-Toastification__toast--rtl {
      margin-right: unset;
      margin-left: auto;
    }
  }
  .Vue-Toastification__container.top-right,
  .Vue-Toastification__container.bottom-right {
    right: 1em;
  }
  .Vue-Toastification__container.top-right .Vue-Toastification__toast,
  .Vue-Toastification__container.bottom-right .Vue-Toastification__toast {
    margin-left: auto;
  }
  @supports not (-moz-appearance: none) {
    .Vue-Toastification__container.top-right .Vue-Toastification__toast--rtl,
    .Vue-Toastification__container.bottom-right .Vue-Toastification__toast--rtl {
      margin-left: unset;
      margin-right: auto;
    }
  }
  .Vue-Toastification__container.top-center,
  .Vue-Toastification__container.bottom-center {
    left: 50%;
    margin-left: -300px;
  }
  .Vue-Toastification__container.top-center .Vue-Toastification__toast,
  .Vue-Toastification__container.bottom-center .Vue-Toastification__toast {
    margin-left: auto;
    margin-right: auto;
  }
}
@media only screen and (max-width : 600px) {
  .Vue-Toastification__container {
    width: 100vw;
    padding: 0;
    left: 0;
    margin: 0;
  }
  .Vue-Toastification__container .Vue-Toastification__toast {
    width: 100%;
  }
  .Vue-Toastification__container.top-left,
  .Vue-Toastification__container.top-right,
  .Vue-Toastification__container.top-center {
    top: 0;
  }
  .Vue-Toastification__container.bottom-left,
  .Vue-Toastification__container.bottom-right,
  .Vue-Toastification__container.bottom-center {
    bottom: 0;
    flex-direction: column-reverse;
  }
}
.Vue-Toastification__toast {
  display: inline-flex;
  position: relative;
  max-height: 800px;
  min-height: 64px;
  box-sizing: border-box;
  margin-bottom: 1rem;
  padding: 22px 24px;
  border-radius: 8px;
  box-shadow: 0 1px 10px 0 rgba(0, 0, 0, 0.1), 0 2px 15px 0 rgba(0, 0, 0, 0.05);
  justify-content: space-between;
  font-family:
    "Lato",
    Helvetica,
    "Roboto",
    Arial,
    sans-serif;
  max-width: 600px;
  min-width: 326px;
  pointer-events: auto;
  overflow: hidden;
  transform: translateZ(0);
  direction: ltr;
}
.Vue-Toastification__toast--rtl {
  direction: rtl;
}
.Vue-Toastification__toast--default {
  background-color: #1976d2;
  color: #fff;
}
.Vue-Toastification__toast--info {
  background-color: #2196f3;
  color: #fff;
}
.Vue-Toastification__toast--success {
  background-color: #4caf50;
  color: #fff;
}
.Vue-Toastification__toast--error {
  background-color: #ff5252;
  color: #fff;
}
.Vue-Toastification__toast--warning {
  background-color: #ffc107;
  color: #fff;
}
@media only screen and (max-width : 600px) {
  .Vue-Toastification__toast {
    border-radius: 0px;
    margin-bottom: 0.5rem;
  }
}
.Vue-Toastification__toast-body {
  flex: 1;
  line-height: 24px;
  font-size: 16px;
  word-break: break-word;
  white-space: pre-wrap;
}
.Vue-Toastification__toast-component-body {
  flex: 1;
}
.Vue-Toastification__toast.disable-transition {
  animation: none !important;
}
.Vue-Toastification__close-button {
  font-weight: bold;
  font-size: 24px;
  line-height: 24px;
  background: transparent;
  outline: none;
  border: none;
  padding: 0;
  padding-left: 10px;
  cursor: pointer;
  transition: 0.3s ease;
  align-items: center;
  color: #fff;
  opacity: 0.3;
  transition: visibility 0s, opacity 0.2s linear;
}
.Vue-Toastification__close-button:hover,
.Vue-Toastification__close-button:focus {
  opacity: 1;
}
.Vue-Toastification__toast:not(:hover) .Vue-Toastification__close-button.show-on-hover {
  opacity: 0;
}
.Vue-Toastification__toast--rtl .Vue-Toastification__close-button {
  padding-left: unset;
  padding-right: 10px;
}
@keyframes scale-x-frames {
  0% {
    transform: scaleX(1);
  }
  100% {
    transform: scaleX(0);
  }
}
.Vue-Toastification__progress-bar {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 5px;
  z-index: 10000;
  background-color: rgba(255, 255, 255, 0.7);
  transform-origin: left;
  animation: scale-x-frames linear 1 forwards;
}
.Vue-Toastification__toast--rtl .Vue-Toastification__progress-bar {
  right: 0;
  left: unset;
  transform-origin: right;
}
.Vue-Toastification__icon {
  margin: auto 18px auto 0px;
  background: transparent;
  outline: none;
  border: none;
  padding: 0;
  transition: 0.3s ease;
  align-items: center;
  width: 20px;
  height: 100%;
}
.Vue-Toastification__toast--rtl .Vue-Toastification__icon {
  margin: auto 0px auto 18px;
}
@keyframes bounceInRight {
  from, 60%, 75%, 90%, to {
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  from {
    opacity: 0;
    transform: translate3d(3000px, 0, 0);
  }
  60% {
    opacity: 1;
    transform: translate3d(-25px, 0, 0);
  }
  75% {
    transform: translate3d(10px, 0, 0);
  }
  90% {
    transform: translate3d(-5px, 0, 0);
  }
  to {
    transform: none;
  }
}
@keyframes bounceOutRight {
  40% {
    opacity: 1;
    transform: translate3d(-20px, 0, 0);
  }
  to {
    opacity: 0;
    transform: translate3d(1000px, 0, 0);
  }
}
@keyframes bounceInLeft {
  from, 60%, 75%, 90%, to {
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  0% {
    opacity: 0;
    transform: translate3d(-3000px, 0, 0);
  }
  60% {
    opacity: 1;
    transform: translate3d(25px, 0, 0);
  }
  75% {
    transform: translate3d(-10px, 0, 0);
  }
  90% {
    transform: translate3d(5px, 0, 0);
  }
  to {
    transform: none;
  }
}
@keyframes bounceOutLeft {
  20% {
    opacity: 1;
    transform: translate3d(20px, 0, 0);
  }
  to {
    opacity: 0;
    transform: translate3d(-2000px, 0, 0);
  }
}
@keyframes bounceInUp {
  from, 60%, 75%, 90%, to {
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  from {
    opacity: 0;
    transform: translate3d(0, 3000px, 0);
  }
  60% {
    opacity: 1;
    transform: translate3d(0, -20px, 0);
  }
  75% {
    transform: translate3d(0, 10px, 0);
  }
  90% {
    transform: translate3d(0, -5px, 0);
  }
  to {
    transform: translate3d(0, 0, 0);
  }
}
@keyframes bounceOutUp {
  20% {
    transform: translate3d(0, -10px, 0);
  }
  40%, 45% {
    opacity: 1;
    transform: translate3d(0, 20px, 0);
  }
  to {
    opacity: 0;
    transform: translate3d(0, -2000px, 0);
  }
}
@keyframes bounceInDown {
  from, 60%, 75%, 90%, to {
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  0% {
    opacity: 0;
    transform: translate3d(0, -3000px, 0);
  }
  60% {
    opacity: 1;
    transform: translate3d(0, 25px, 0);
  }
  75% {
    transform: translate3d(0, -10px, 0);
  }
  90% {
    transform: translate3d(0, 5px, 0);
  }
  to {
    transform: none;
  }
}
@keyframes bounceOutDown {
  20% {
    transform: translate3d(0, 10px, 0);
  }
  40%, 45% {
    opacity: 1;
    transform: translate3d(0, -20px, 0);
  }
  to {
    opacity: 0;
    transform: translate3d(0, 2000px, 0);
  }
}
.Vue-Toastification__bounce-enter-active.top-left,
.Vue-Toastification__bounce-enter-active.bottom-left {
  animation-name: bounceInLeft;
}
.Vue-Toastification__bounce-enter-active.top-right,
.Vue-Toastification__bounce-enter-active.bottom-right {
  animation-name: bounceInRight;
}
.Vue-Toastification__bounce-enter-active.top-center {
  animation-name: bounceInDown;
}
.Vue-Toastification__bounce-enter-active.bottom-center {
  animation-name: bounceInUp;
}
.Vue-Toastification__bounce-leave-active:not(.disable-transition).top-left,
.Vue-Toastification__bounce-leave-active:not(.disable-transition).bottom-left {
  animation-name: bounceOutLeft;
}
.Vue-Toastification__bounce-leave-active:not(.disable-transition).top-right,
.Vue-Toastification__bounce-leave-active:not(.disable-transition).bottom-right {
  animation-name: bounceOutRight;
}
.Vue-Toastification__bounce-leave-active:not(.disable-transition).top-center {
  animation-name: bounceOutUp;
}
.Vue-Toastification__bounce-leave-active:not(.disable-transition).bottom-center {
  animation-name: bounceOutDown;
}
.Vue-Toastification__bounce-leave-active,
.Vue-Toastification__bounce-enter-active {
  animation-duration: 750ms;
  animation-fill-mode: both;
}
.Vue-Toastification__bounce-move {
  transition-timing-function: ease-in-out;
  transition-property: all;
  transition-duration: 400ms;
}
@keyframes fadeOutTop {
  0% {
    transform: translateY(0);
    opacity: 1;
  }
  100% {
    transform: translateY(-50px);
    opacity: 0;
  }
}
@keyframes fadeOutLeft {
  0% {
    transform: translateX(0);
    opacity: 1;
  }
  100% {
    transform: translateX(-50px);
    opacity: 0;
  }
}
@keyframes fadeOutBottom {
  0% {
    transform: translateY(0);
    opacity: 1;
  }
  100% {
    transform: translateY(50px);
    opacity: 0;
  }
}
@keyframes fadeOutRight {
  0% {
    transform: translateX(0);
    opacity: 1;
  }
  100% {
    transform: translateX(50px);
    opacity: 0;
  }
}
@keyframes fadeInLeft {
  0% {
    transform: translateX(-50px);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}
@keyframes fadeInRight {
  0% {
    transform: translateX(50px);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}
@keyframes fadeInTop {
  0% {
    transform: translateY(-50px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}
@keyframes fadeInBottom {
  0% {
    transform: translateY(50px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}
.Vue-Toastification__fade-enter-active.top-left,
.Vue-Toastification__fade-enter-active.bottom-left {
  animation-name: fadeInLeft;
}
.Vue-Toastification__fade-enter-active.top-right,
.Vue-Toastification__fade-enter-active.bottom-right {
  animation-name: fadeInRight;
}
.Vue-Toastification__fade-enter-active.top-center {
  animation-name: fadeInTop;
}
.Vue-Toastification__fade-enter-active.bottom-center {
  animation-name: fadeInBottom;
}
.Vue-Toastification__fade-leave-active:not(.disable-transition).top-left,
.Vue-Toastification__fade-leave-active:not(.disable-transition).bottom-left {
  animation-name: fadeOutLeft;
}
.Vue-Toastification__fade-leave-active:not(.disable-transition).top-right,
.Vue-Toastification__fade-leave-active:not(.disable-transition).bottom-right {
  animation-name: fadeOutRight;
}
.Vue-Toastification__fade-leave-active:not(.disable-transition).top-center {
  animation-name: fadeOutTop;
}
.Vue-Toastification__fade-leave-active:not(.disable-transition).bottom-center {
  animation-name: fadeOutBottom;
}
.Vue-Toastification__fade-leave-active,
.Vue-Toastification__fade-enter-active {
  animation-duration: 750ms;
  animation-fill-mode: both;
}
.Vue-Toastification__fade-move {
  transition-timing-function: ease-in-out;
  transition-property: all;
  transition-duration: 400ms;
}
@keyframes slideInBlurredLeft {
  0% {
    transform: translateX(-1000px) scaleX(2.5) scaleY(0.2);
    transform-origin: 100% 50%;
    filter: blur(40px);
    opacity: 0;
  }
  100% {
    transform: translateX(0) scaleY(1) scaleX(1);
    transform-origin: 50% 50%;
    filter: blur(0);
    opacity: 1;
  }
}
@keyframes slideInBlurredTop {
  0% {
    transform: translateY(-1000px) scaleY(2.5) scaleX(0.2);
    transform-origin: 50% 0%;
    filter: blur(240px);
    opacity: 0;
  }
  100% {
    transform: translateY(0) scaleY(1) scaleX(1);
    transform-origin: 50% 50%;
    filter: blur(0);
    opacity: 1;
  }
}
@keyframes slideInBlurredRight {
  0% {
    transform: translateX(1000px) scaleX(2.5) scaleY(0.2);
    transform-origin: 0% 50%;
    filter: blur(40px);
    opacity: 0;
  }
  100% {
    transform: translateX(0) scaleY(1) scaleX(1);
    transform-origin: 50% 50%;
    filter: blur(0);
    opacity: 1;
  }
}
@keyframes slideInBlurredBottom {
  0% {
    transform: translateY(1000px) scaleY(2.5) scaleX(0.2);
    transform-origin: 50% 100%;
    filter: blur(240px);
    opacity: 0;
  }
  100% {
    transform: translateY(0) scaleY(1) scaleX(1);
    transform-origin: 50% 50%;
    filter: blur(0);
    opacity: 1;
  }
}
@keyframes slideOutBlurredTop {
  0% {
    transform: translateY(0) scaleY(1) scaleX(1);
    transform-origin: 50% 0%;
    filter: blur(0);
    opacity: 1;
  }
  100% {
    transform: translateY(-1000px) scaleY(2) scaleX(0.2);
    transform-origin: 50% 0%;
    filter: blur(240px);
    opacity: 0;
  }
}
@keyframes slideOutBlurredBottom {
  0% {
    transform: translateY(0) scaleY(1) scaleX(1);
    transform-origin: 50% 50%;
    filter: blur(0);
    opacity: 1;
  }
  100% {
    transform: translateY(1000px) scaleY(2) scaleX(0.2);
    transform-origin: 50% 100%;
    filter: blur(240px);
    opacity: 0;
  }
}
@keyframes slideOutBlurredLeft {
  0% {
    transform: translateX(0) scaleY(1) scaleX(1);
    transform-origin: 50% 50%;
    filter: blur(0);
    opacity: 1;
  }
  100% {
    transform: translateX(-1000px) scaleX(2) scaleY(0.2);
    transform-origin: 100% 50%;
    filter: blur(40px);
    opacity: 0;
  }
}
@keyframes slideOutBlurredRight {
  0% {
    transform: translateX(0) scaleY(1) scaleX(1);
    transform-origin: 50% 50%;
    filter: blur(0);
    opacity: 1;
  }
  100% {
    transform: translateX(1000px) scaleX(2) scaleY(0.2);
    transform-origin: 0% 50%;
    filter: blur(40px);
    opacity: 0;
  }
}
.Vue-Toastification__slideBlurred-enter-active.top-left,
.Vue-Toastification__slideBlurred-enter-active.bottom-left {
  animation-name: slideInBlurredLeft;
}
.Vue-Toastification__slideBlurred-enter-active.top-right,
.Vue-Toastification__slideBlurred-enter-active.bottom-right {
  animation-name: slideInBlurredRight;
}
.Vue-Toastification__slideBlurred-enter-active.top-center {
  animation-name: slideInBlurredTop;
}
.Vue-Toastification__slideBlurred-enter-active.bottom-center {
  animation-name: slideInBlurredBottom;
}
.Vue-Toastification__slideBlurred-leave-active:not(.disable-transition).top-left,
.Vue-Toastification__slideBlurred-leave-active:not(.disable-transition).bottom-left {
  animation-name: slideOutBlurredLeft;
}
.Vue-Toastification__slideBlurred-leave-active:not(.disable-transition).top-right,
.Vue-Toastification__slideBlurred-leave-active:not(.disable-transition).bottom-right {
  animation-name: slideOutBlurredRight;
}
.Vue-Toastification__slideBlurred-leave-active:not(.disable-transition).top-center {
  animation-name: slideOutBlurredTop;
}
.Vue-Toastification__slideBlurred-leave-active:not(.disable-transition).bottom-center {
  animation-name: slideOutBlurredBottom;
}
.Vue-Toastification__slideBlurred-leave-active,
.Vue-Toastification__slideBlurred-enter-active {
  animation-duration: 750ms;
  animation-fill-mode: both;
}
.Vue-Toastification__slideBlurred-move {
  transition-timing-function: ease-in-out;
  transition-property: all;
  transition-duration: 400ms;
}

