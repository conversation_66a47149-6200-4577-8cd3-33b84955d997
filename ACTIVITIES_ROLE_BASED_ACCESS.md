# Role-Based Activities Access Implementation

## Overview
Implemented comprehensive role-based access control for the NDT Activities system, including viewing, editing, and deleting activities.

## API Endpoints
**GET** `/api/v1/ndyt-activities/activities`
**PUT** `/api/v1/ndyt-activities/activities/:id`
**DELETE** `/api/v1/ndyt-activities/activities/:id`

## Authentication
- Requires Bear<PERSON> token authentication
- Uses existing JWT token system

## Role-Based Access Rules

### Viewing Activities (GET /activities)

#### Admin Users (`rank: 'admin'`)
- **Access**: See ALL activities from ALL governorates
- **Query**: No filtering applied
- **Use Case**: System-wide oversight and management

#### Leader Users (`rank: 'leader'`)
- **Access**: See ALL activities from THEIR governorate only
- **Query**: Filtered by `governorate` matching user's governorate
- **Use Case**: Regional coordination and oversight

#### Member Users (`rank: 'member'`)
- **Access**: See ONLY their own submitted activities
- **Query**: Filtered by `user_id` matching current user
- **Use Case**: Personal activity tracking

### Editing Activities (PUT /activities/:id)

#### Admin Users (`rank: 'admin'`)
- **Permission**: Can edit ALL activities from ALL users and governorates
- **Use Case**: System-wide activity management and corrections

#### Leader Users (`rank: 'leader'`)
- **Permission**: Can edit ALL activities from THEIR governorate only
- **Restriction**: Cannot edit activities from other governorates
- **Use Case**: Regional activity management and oversight

#### Member Users (`rank: 'member'`)
- **Permission**: Can edit ONLY their own submitted activities
- **Restriction**: Cannot edit activities submitted by other users
- **Use Case**: Personal activity updates and corrections

### Deleting Activities (DELETE /activities/:id)

#### Admin Users (`rank: 'admin'`)
- **Permission**: Can delete ALL activities from ALL users and governorates
- **Use Case**: System-wide activity cleanup and management

#### Leader Users (`rank: 'leader'`)
- **Permission**: Can delete ALL activities from THEIR governorate only
- **Restriction**: Cannot delete activities from other governorates
- **Use Case**: Regional activity management

#### Member Users (`rank: 'member'`)
- **Permission**: Can delete ONLY their own submitted activities
- **Restriction**: Cannot delete activities submitted by other users
- **Use Case**: Personal activity management

## Response Format
```json
{
  "activities": [
    {
      "id": 1,
      "submission_id": 1,
      "owner_name": "Activity Owner",
      "title": "Activity Title",
      "short_description": "Description",
      "activity_date": "2025-01-01",
      "state": "منفذ بصرف",
      "created_at": "2025-01-01T00:00:00.000Z",
      "governorate": "بغداد",
      "coordinator_name": "Coordinator Name",
      "user_id": 1,
      "username": "submitter_username",
      "submitter_name": "Full Name",
      "files": [
        {
          "id": 1,
          "file_name": "document.pdf",
          "file_url": "https://example.com/file.pdf",
          "mime_type": "application/pdf",
          "size_bytes": 1024,
          "uploaded_at": "2025-01-01T00:00:00.000Z"
        }
      ]
    }
  ],
  "user_role": "admin|leader|member",
  "total_count": 1
}
```

## Database Schema
The implementation uses the existing schema:
- `ndt_users` table with `rank`, `governorate` columns
- `ndt_activities` table
- `ndt_activity_submissions` table
- `ndt_activity_files` table

## Role-Based Access Control
The system supports three user ranks: `admin`, `leader`, and `member`. All ranks are stored directly in the `rank` column of the `ndt_users` table.

## Security Features
- JWT token validation
- Role-based query filtering at database level
- No data leakage between roles
- Proper error handling for unauthorized access

## Usage Examples

### Frontend Integration
```javascript
// Fetch activities based on user role
const response = await fetch('/api/v1/ndyt-activities/activities', {
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  }
});

const data = await response.json();
console.log(`User role: ${data.user_role}`);
console.log(`Total activities: ${data.total_count}`);
```

## Error Responses

### Common Errors (All Endpoints)
- `401`: Authentication required (missing or invalid token)
- `404`: User not found
- `500`: Server error

### Edit/Delete Specific Errors
- `400`: Invalid activity ID or missing required fields
- `403`: Access denied - insufficient permissions to edit/delete this activity
- `404`: Activity not found

## Implementation Notes
- **Comprehensive Role-Based Access Control**: Implemented across viewing, editing, and deleting operations
- **Permission Validation**: Each edit/delete request validates user permissions before allowing the operation
- **Direct Rank Storage**: All user ranks are stored directly in the `rank` column for consistent access control
- **Governorate-Based Filtering**: Leaders can only manage activities within their assigned governorate
- **Ownership Validation**: Regular users can only manage their own submitted activities
- **Security**: Database-level permission checks prevent unauthorized access
- **Error Handling**: Clear error messages for permission denials and invalid requests
- **Activities Ordering**: Results ordered by `created_at DESC` (newest first)
- **File Inclusion**: Associated files included for each activity
- **Efficient Queries**: Optimized JOIN queries to minimize database calls
- **Arabic Support**: Supports existing Arabic activity states
- **Frontend Compatibility**: Compatible with existing Vue.js application