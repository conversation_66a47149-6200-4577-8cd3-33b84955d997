-- Migration to fix foreign key constraints for user deletion
-- This addresses the issue where ndt_users cannot be deleted due to 
-- references from site_deployments table (different app)

-- Option 1: Update the foreign key constraint to SET NULL on delete
-- This is safer as it preserves deployment history while allowing user deletion

-- First, drop the existing constraint
ALTER TABLE site_deployments 
DROP CONSTRAINT IF EXISTS site_deployments_deployed_by_fkey;

-- Add the constraint back with SET NULL on delete
ALTER TABLE site_deployments 
ADD CONSTRAINT site_deployments_deployed_by_fkey 
FOREIGN KEY (deployed_by) REFERENCES ndt_users(id) ON DELETE SET NULL;

-- Verification: Check the constraint
SELECT 
    tc.constraint_name,
    tc.table_name,
    kcu.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name,
    rc.delete_rule
FROM information_schema.table_constraints AS tc 
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
JOIN information_schema.referential_constraints AS rc
    ON tc.constraint_name = rc.constraint_name
WHERE tc.constraint_type = 'FOREIGN KEY' 
    AND tc.constraint_name = 'site_deployments_deployed_by_fkey';
