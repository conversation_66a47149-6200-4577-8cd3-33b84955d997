-- Migration to add 'super_admin' rank and assign it to mode<PERSON><PERSON> user
-- Run this when database write access is available

-- Step 1: Drop the existing rank constraint
ALTER TABLE ndt_users DROP CONSTRAINT ndt_users_rank_check;

-- Step 2: Add new constraint that includes 'super_admin'
ALTER TABLE ndt_users ADD CONSTRAINT ndt_users_rank_check
    CHECK (rank IN ('admin', 'member', 'leader', 'super_admin'));

-- Step 3: Update moderfaris user to have super_admin rank
UPDATE ndt_users
SET rank = 'super_admin'
WHERE username = 'moderfaris';

-- Verification queries
SELECT 'Super admin users:' as info;
SELECT username, rank FROM ndt_users WHERE rank = 'super_admin';

SELECT 'All rank counts:' as info;
SELECT rank, COUNT(*) FROM ndt_users GROUP BY rank ORDER BY rank;
